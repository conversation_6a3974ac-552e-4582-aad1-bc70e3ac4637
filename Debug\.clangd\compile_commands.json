[{"directory": "C:/Users/<USER>/Desktop/777/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/777/bsp\" -I\"C:/Users/<USER>/Desktop/777/app\" -I\"C:/Users/<USER>/Desktop/777/app/OLED\" -I\"C:/Users/<USER>/Desktop/777\" -I\"C:/Users/<USER>/Desktop/777/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/777/Debug/ti_msp_dl_config.c"}, {"directory": "C:/Users/<USER>/Desktop/777/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/777/bsp\" -I\"C:/Users/<USER>/Desktop/777/app\" -I\"C:/Users/<USER>/Desktop/777/app/OLED\" -I\"C:/Users/<USER>/Desktop/777\" -I\"C:/Users/<USER>/Desktop/777/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/777/app/Ganway.c"}, {"directory": "C:/Users/<USER>/Desktop/777/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/777/bsp\" -I\"C:/Users/<USER>/Desktop/777/app\" -I\"C:/Users/<USER>/Desktop/777/app/OLED\" -I\"C:/Users/<USER>/Desktop/777\" -I\"C:/Users/<USER>/Desktop/777/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/777/app/No_Mcu_Ganv_Grayscale_Sensor.c"}, {"directory": "C:/Users/<USER>/Desktop/777/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/777/bsp\" -I\"C:/Users/<USER>/Desktop/777/app\" -I\"C:/Users/<USER>/Desktop/777/app/OLED\" -I\"C:/Users/<USER>/Desktop/777\" -I\"C:/Users/<USER>/Desktop/777/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/777/app/OLED/oled.c"}, {"directory": "C:/Users/<USER>/Desktop/777/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/777/bsp\" -I\"C:/Users/<USER>/Desktop/777/app\" -I\"C:/Users/<USER>/Desktop/777/app/OLED\" -I\"C:/Users/<USER>/Desktop/777\" -I\"C:/Users/<USER>/Desktop/777/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/777/app/Scheduler.c"}, {"directory": "C:/Users/<USER>/Desktop/777/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/777/bsp\" -I\"C:/Users/<USER>/Desktop/777/app\" -I\"C:/Users/<USER>/Desktop/777/app/OLED\" -I\"C:/Users/<USER>/Desktop/777\" -I\"C:/Users/<USER>/Desktop/777/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/777/app/encoder.c"}, {"directory": "C:/Users/<USER>/Desktop/777/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/777/bsp\" -I\"C:/Users/<USER>/Desktop/777/app\" -I\"C:/Users/<USER>/Desktop/777/app/OLED\" -I\"C:/Users/<USER>/Desktop/777\" -I\"C:/Users/<USER>/Desktop/777/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/777/app/key.c"}, {"directory": "C:/Users/<USER>/Desktop/777/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/777/bsp\" -I\"C:/Users/<USER>/Desktop/777/app\" -I\"C:/Users/<USER>/Desktop/777/app/OLED\" -I\"C:/Users/<USER>/Desktop/777\" -I\"C:/Users/<USER>/Desktop/777/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/777/app/motor.c"}, {"directory": "C:/Users/<USER>/Desktop/777/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/777/bsp\" -I\"C:/Users/<USER>/Desktop/777/app\" -I\"C:/Users/<USER>/Desktop/777/app/OLED\" -I\"C:/Users/<USER>/Desktop/777\" -I\"C:/Users/<USER>/Desktop/777/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/777/app/ringbuffer.c"}, {"directory": "C:/Users/<USER>/Desktop/777/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/777/bsp\" -I\"C:/Users/<USER>/Desktop/777/app\" -I\"C:/Users/<USER>/Desktop/777/app/OLED\" -I\"C:/Users/<USER>/Desktop/777\" -I\"C:/Users/<USER>/Desktop/777/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/777/bsp/bsp_usart.c"}, {"directory": "C:/Users/<USER>/Desktop/777/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/777/bsp\" -I\"C:/Users/<USER>/Desktop/777/app\" -I\"C:/Users/<USER>/Desktop/777/app/OLED\" -I\"C:/Users/<USER>/Desktop/777\" -I\"C:/Users/<USER>/Desktop/777/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/777/bsp/systick.c"}, {"directory": "C:/Users/<USER>/Desktop/777/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/777/bsp\" -I\"C:/Users/<USER>/Desktop/777/app\" -I\"C:/Users/<USER>/Desktop/777/app/OLED\" -I\"C:/Users/<USER>/Desktop/777\" -I\"C:/Users/<USER>/Desktop/777/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/777/empty.c"}]