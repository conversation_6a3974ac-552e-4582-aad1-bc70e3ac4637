******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 05:58:47 2025

OUTPUT FILE NAME:   <test1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00002a35


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00004c70  0001b390  R  X
  SRAM                  20200000   00008000  00000925  000076db  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004c70   00004c70    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00003108   00003108    r-x .text
  000031c8    000031c8    00001a50   00001a50    r-- .rodata
  00004c18    00004c18    00000058   00000058    r-- .cinit
20200000    20200000    0000072c   00000000    rw-
  20200000    20200000    00000561   00000000    rw- .bss
  20200568    20200568    000001c4   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00003108     
                  000000c0    00000580     Ganway.o (.text.Way)
                  00000640    000001d0     oled.o (.text.OLED_ShowChar)
                  00000810    00000194     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000009a4    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00000b36    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000b38    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00000cc0    00000120     encoder.o (.text.GROUP1_IRQHandler)
                  00000de0    00000118     empty.o (.text.main)
                  00000ef8    0000010c     motor.o (.text.Set_PWM)
                  00001004    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001110    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00001214    000000e8                 : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000012fc    000000e4     empty.o (.text.TIMG0_IRQHandler)
                  000013e0    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000014c4    000000e2     oled.o (.text.OLED_ShowNum)
                  000015a6    000000de     oled.o (.text.OLED_Init)
                  00001684    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00001760    000000d0     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  00001830    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  000018da    0000009a     oled.o (.text.OLED_ShowSignedNum)
                  00001974    0000009a     oled.o (.text.OLED_ShowString)
                  00001a0e    00000002     --HOLE-- [fill = 0]
                  00001a10    00000090     oled.o (.text.OLED_DrawPoint)
                  00001aa0    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00001b2c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001bb8    00000084     oled.o (.text.OLED_Refresh)
                  00001c3c    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00001cc0    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00001d3c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00001db0    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00001e22    00000002     --HOLE-- [fill = 0]
                  00001e24    0000006c     oled.o (.text.OLED_WR_Byte)
                  00001e90    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00001efc    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00001f64    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00001fc6    00000002     --HOLE-- [fill = 0]
                  00001fc8    00000060     oled.o (.text.OLED_Clear)
                  00002028    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00002086    00000002     --HOLE-- [fill = 0]
                  00002088    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000020e0    00000054     key.o (.text.Key)
                  00002134    00000054     key.o (.text.Key_1)
                  00002188    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  000021dc    00000050     oled.o (.text.DL_I2C_startControllerTransfer)
                  0000222c    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  0000227c    0000004c     empty.o (.text.DL_ADC12_initSingleSample)
                  000022c8    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00002314    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  0000235e    00000002     --HOLE-- [fill = 0]
                  00002360    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000023aa    0000004a     No_Mcu_Ganv_Grayscale_Sensor.o (.text.adc_getValue)
                  000023f4    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  0000243c    00000048     oled.o (.text.OLED_DisplayTurn)
                  00002484    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  000024cc    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002514    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00002558    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  0000259a    00000002     --HOLE-- [fill = 0]
                  0000259c    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  000025dc    00000040     bsp_usart.o (.text.UART0_IRQHandler)
                  0000261c    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  0000265c    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00002698    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000026d4    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00002710    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000274c    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  00002786    00000002     --HOLE-- [fill = 0]
                  00002788    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000027bc    00000034     oled.o (.text.OLED_ColorTurn)
                  000027f0    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00002824    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00002858    00000030     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getMemResult)
                  00002888    00000030     oled.o (.text.OLED_Pow)
                  000028b8    00000030     systick.o (.text.SysTick_Handler)
                  000028e8    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  00002914    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  00002940    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  0000296c    00000028     empty.o (.text.DL_Common_updateReg)
                  00002994    00000028     oled.o (.text.DL_Common_updateReg)
                  000029bc    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000029e4    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00002a0c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00002a34    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00002a5c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00002a82    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00002aa8    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00002acc    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00002aec    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00002b0c    00000020     systick.o (.text.delay_ms)
                  00002b2c    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00002b4a    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00002b68    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_startConversion)
                  00002b84    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_stopConversion)
                  00002ba0    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00002bbc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00002bd8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00002bf4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00002c10    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00002c2c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00002c48    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00002c64    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00002c80    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00002c9c    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00002cb8    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00002cd4    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00002cec    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00002d04    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00002d1c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00002d34    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00002d4c    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00002d64    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00002d7c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00002d94    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00002dac    00000018     empty.o (.text.DL_GPIO_setPins)
                  00002dc4    00000018     motor.o (.text.DL_GPIO_setPins)
                  00002ddc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00002df4    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00002e0c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00002e24    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00002e3c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00002e54    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00002e6c    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00002e84    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00002e9c    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00002eb4    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00002ecc    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00002ee4    00000018     empty.o (.text.DL_Timer_startCounter)
                  00002efc    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00002f14    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00002f2c    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_disableConversions)
                  00002f42    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_enableConversions)
                  00002f58    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00002f6e    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00002f84    00000016     key.o (.text.DL_GPIO_readPins)
                  00002f9a    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00002fb0    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00002fc4    00000014     empty.o (.text.DL_GPIO_clearPins)
                  00002fd8    00000014     motor.o (.text.DL_GPIO_clearPins)
                  00002fec    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00003000    00000014     oled.o (.text.DL_I2C_getControllerStatus)
                  00003014    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00003028    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  0000303c    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00003050    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00003064    00000014     bsp_usart.o (.text.DL_UART_receiveData)
                  00003078    00000014     motor.o (.text.Left_Control)
                  0000308c    00000014     motor.o (.text.Right_Control)
                  000030a0    00000014     motor.o (.text.Right_Little_Control)
                  000030b4    00000012     empty.o (.text.DL_Timer_getPendingInterrupt)
                  000030c6    00000012     bsp_usart.o (.text.DL_UART_getPendingInterrupt)
                  000030d8    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000030ea    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000030fc    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000310e    00000010     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getStatus)
                  0000311e    00000002     --HOLE-- [fill = 0]
                  00003120    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00003130    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00003140    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  00003150    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  0000315e    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  0000316c    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00003178    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00003184    0000000c     systick.o (.text.get_systicks)
                  00003190    0000000c     Scheduler.o (.text.scheduler_init)
                  0000319c    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000031a6    00000002     --HOLE-- [fill = 0]
                  000031a8    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000031b0    00000006     libc.a : exit.c.obj (.text:abort)
                  000031b6    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000031ba    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000031be    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000031c2    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000031c6    00000002     --HOLE-- [fill = 0]

.cinit     0    00004c18    00000058     
                  00004c18    0000002f     (.cinit..data.load) [load image, compression = lzss]
                  00004c47    00000001     --HOLE-- [fill = 0]
                  00004c48    0000000c     (__TI_handler_table)
                  00004c54    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004c5c    00000010     (__TI_cinit_table)
                  00004c6c    00000004     --HOLE-- [fill = 0]

.rodata    0    000031c8    00001a50     
                  000031c8    00000d5c     oled.o (.rodata.asc2_2412)
                  00003f24    000005f0     oled.o (.rodata.asc2_1608)
                  00004514    00000474     oled.o (.rodata.asc2_1206)
                  00004988    00000228     oled.o (.rodata.asc2_0806)
                  00004bb0    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00004bd8    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00004bec    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00004bf6    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00004bf8    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00004c00    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  00004c08    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  00004c0b    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00004c0e    00000003     empty.o (.rodata.str1.9517790425240694019.1)
                  00004c11    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00004c13    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000561     UNINITIALIZED
                  20200000    00000480     (.common:OLED_GRAM)
                  20200480    000000bc     (.common:gPWM_0Backup)
                  2020053c    00000004     (.common:Flag_stop)
                  20200540    00000004     (.common:Flag_stop1)
                  20200544    00000004     (.common:Get_Encoder_countA)
                  20200548    00000004     (.common:Get_Encoder_countB)
                  2020054c    00000004     (.common:Run)
                  20200550    00000004     (.common:encoderA_cnt)
                  20200554    00000004     (.common:encoderB_cnt)
                  20200558    00000004     (.common:gpio_interrup1)
                  2020055c    00000004     (.common:gpio_interrup2)
                  20200560    00000001     (.common:task_num)

.data      0    20200568    000001c4     UNINITIALIZED
                  20200568    00000100     empty.o (.data.rx_buff)
                  20200668    00000080     bsp_usart.o (.data.uart_rx_buffer)
                  202006e8    00000010     empty.o (.data.Anolog)
                  202006f8    00000010     empty.o (.data.black)
                  20200708    00000010     empty.o (.data.white)
                  20200718    00000008     systick.o (.data.systicks)
                  20200720    00000004     empty.o (.data.D_Num)
                  20200724    00000004     systick.o (.data.delay_times)
                  20200728    00000001     key.o (.data.Key.key_pressed)
                  20200729    00000001     key.o (.data.Key_1.key1_pressed)
                  2020072a    00000001     bsp_usart.o (.data.uart_rx_index)
                  2020072b    00000001     bsp_usart.o (.data.uart_rx_ticks)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               2772    96        188    
       empty.o                          798     3         328    
       startup_mspm0g350x_ticlang.o     8       192       0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3578    291       516    
                                                                 
    .\app\
       No_Mcu_Ganv_Grayscale_Sensor.o   1414    0         0      
       Ganway.o                         1408    0         0      
       encoder.o                        362     0         16     
       motor.o                          372     0         0      
       key.o                            190     0         2      
       Scheduler.o                      12      0         1      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3758    0         19     
                                                                 
    .\app\OLED\
       oled.o                           2012    6632      1152   
    +--+--------------------------------+-------+---------+---------+
       Total:                           2012    6632      1152   
                                                                 
    .\bsp\
       bsp_usart.o                      102     0         130    
       systick.o                        92      0         12     
    +--+--------------------------------+-------+---------+---------+
       Total:                           194     0         142    
                                                                 
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                       588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o     288     0         0      
       dl_i2c.o                         132     0         0      
       dl_uart.o                        90      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1172    0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj       124     0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              40      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       copy_zero_init.c.obj             16      0         0      
       memset16.S.obj                   14      0         0      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           300     0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     402     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       fixdfsi.S.obj                    74      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       muldsi3.S.obj                    58      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsidf.S.obj                36      0         0      
       aeabi_memset.S.obj               12      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 2       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1514    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       83        0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     12532   7006      2341   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004c5c records: 2, size/record: 8, table size: 16
	.data: load addr=00004c18, load size=0000002f bytes, run addr=20200568, run size=000001c4 bytes, compression=lzss
	.bss: load addr=00004c54, load size=00000008 bytes, run addr=20200000, run size=00000561 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004c48 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000031b7  ADC0_IRQHandler                      
000031b7  ADC1_IRQHandler                      
000031b7  AES_IRQHandler                       
202006e8  Anolog                               
000031ba  C$$EXIT                              
000031b7  CANFD0_IRQHandler                    
000031b7  DAC0_IRQHandler                      
0000259d  DL_ADC12_setClockConfig              
0000319d  DL_Common_delayCycles                
00002029  DL_I2C_fillControllerTXFIFO          
00002a83  DL_I2C_setClockConfig                
00001685  DL_SYSCTL_configSYSPLL               
00002515  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001111  DL_Timer_initFourCCPWMMode           
00001215  DL_Timer_initTimerMode               
00002c81  DL_Timer_setCaptCompUpdateMethod     
00002ecd  DL_Timer_setCaptureCompareOutCtl     
00003131  DL_Timer_setCaptureCompareValue      
00002c9d  DL_Timer_setClockConfig              
000023f5  DL_UART_init                         
000030d9  DL_UART_setClockConfig               
000031b7  DMA_IRQHandler                       
20200720  D_Num                                
000031b7  Default_Handler                      
2020053c  Flag_stop                            
20200540  Flag_stop1                           
000031b7  GROUP0_IRQHandler                    
00000cc1  GROUP1_IRQHandler                    
00001761  Get_Analog_value                     
000026d5  Get_Anolog_Value                     
00003151  Get_Digtal_For_User                  
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
000031bb  HOSTexit                             
000031b7  HardFault_Handler                    
000031b7  I2C0_IRQHandler                      
000031b7  I2C1_IRQHandler                      
000020e1  Key                                  
00002135  Key_1                                
00003079  Left_Control                         
000031b7  NMI_Handler                          
00000b39  No_MCU_Ganv_Sensor_Init              
00001db1  No_MCU_Ganv_Sensor_Init_Frist        
00002559  No_Mcu_Ganv_Sensor_Task_Without_tick 
00001fc9  OLED_Clear                           
000027bd  OLED_ColorTurn                       
0000243d  OLED_DisplayTurn                     
00001a11  OLED_DrawPoint                       
20200000  OLED_GRAM                            
000015a7  OLED_Init                            
00002889  OLED_Pow                             
00001bb9  OLED_Refresh                         
00000641  OLED_ShowChar                        
000014c5  OLED_ShowNum                         
000018db  OLED_ShowSignedNum                   
00001975  OLED_ShowString                      
00001e25  OLED_WR_Byte                         
000031b7  PendSV_Handler                       
000031b7  RTC_IRQHandler                       
000031bf  Reset_Handler                        
0000308d  Right_Control                        
000030a1  Right_Little_Control                 
2020054c  Run                                  
000031b7  SPI0_IRQHandler                      
000031b7  SPI1_IRQHandler                      
000031b7  SVC_Handler                          
00002485  SYSCFG_DL_ADC12_0_init               
00000811  SYSCFG_DL_GPIO_init                  
00002089  SYSCFG_DL_I2C_OLED_init              
00001aa1  SYSCFG_DL_PWM_0_init                 
000024cd  SYSCFG_DL_SYSCTL_init                
0000316d  SYSCFG_DL_SYSTICK_init               
000027f1  SYSCFG_DL_TIMER_0_init               
00002189  SYSCFG_DL_UART_0_init                
00002825  SYSCFG_DL_init                       
00001b2d  SYSCFG_DL_initPower                  
00000ef9  Set_PWM                              
000028b9  SysTick_Handler                      
000031b7  TIMA0_IRQHandler                     
000031b7  TIMA1_IRQHandler                     
000012fd  TIMG0_IRQHandler                     
000031b7  TIMG12_IRQHandler                    
000031b7  TIMG6_IRQHandler                     
000031b7  TIMG7_IRQHandler                     
000031b7  TIMG8_IRQHandler                     
000030eb  TI_memcpy_small                      
0000315f  TI_memset_small                      
000025dd  UART0_IRQHandler                     
000031b7  UART1_IRQHandler                     
000031b7  UART2_IRQHandler                     
000031b7  UART3_IRQHandler                     
000000c1  Way                                  
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00004c5c  __TI_CINIT_Base                      
00004c6c  __TI_CINIT_Limit                     
00004c6c  __TI_CINIT_Warm                      
00004c48  __TI_Handler_Table_Base              
00004c54  __TI_Handler_Table_Limit             
00002711  __TI_auto_init_nobinit_nopinit       
00001cc1  __TI_decompress_lzss                 
000030fd  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00003141  __TI_zero_init                       
000009af  __adddf3                             
00002361  __aeabi_d2iz                         
000009af  __aeabi_dadd                         
00001f65  __aeabi_dcmpeq                       
00001fa1  __aeabi_dcmpge                       
00001fb5  __aeabi_dcmpgt                       
00001f8d  __aeabi_dcmple                       
00001f79  __aeabi_dcmplt                       
00001005  __aeabi_ddiv                         
000013e1  __aeabi_dmul                         
000009a5  __aeabi_dsub                         
00002941  __aeabi_i2d                          
00000b37  __aeabi_idiv0                        
00003179  __aeabi_memclr                       
00003179  __aeabi_memclr4                      
00003179  __aeabi_memclr8                      
000031a9  __aeabi_memcpy                       
000031a9  __aeabi_memcpy4                      
000031a9  __aeabi_memcpy8                      
00002aa9  __aeabi_ui2d                         
0000261d  __aeabi_uidiv                        
0000261d  __aeabi_uidivmod                     
ffffffff  __binit__                            
00001efd  __cmpdf2                             
00001005  __divdf3                             
00001efd  __eqdf2                              
00002361  __fixdfsi                            
00002941  __floatsidf                          
00002aa9  __floatunsidf                        
00001d3d  __gedf2                              
00001d3d  __gtdf2                              
00001efd  __ledf2                              
00001efd  __ltdf2                              
UNDEFED   __mpu_init                           
000013e1  __muldf3                             
0000274d  __muldsi3                            
00001efd  __nedf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000009a5  __subdf3                             
00002a35  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
000031c3  _system_pre_init                     
000031b1  abort                                
000023ab  adc_getValue                         
00004988  asc2_0806                            
00004514  asc2_1206                            
00003f24  asc2_1608                            
000031c8  asc2_2412                            
ffffffff  binit                                
202006f8  black                                
00001e91  convertAnalogToDigital               
00002b0d  delay_ms                             
20200724  delay_times                          
20200550  encoderA_cnt                         
20200554  encoderB_cnt                         
20200480  gPWM_0Backup                         
00003185  get_systicks                         
20200558  gpio_interrup1                       
2020055c  gpio_interrup2                       
00000000  interruptVectors                     
00000de1  main                                 
00001831  normalizeAnalogValues                
20200568  rx_buff                              
00003191  scheduler_init                       
20200560  task_num                             
20200668  uart_rx_buffer                       
2020072a  uart_rx_index                        
2020072b  uart_rx_ticks                        
20200708  white                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  Way                                  
00000200  __STACK_SIZE                         
00000641  OLED_ShowChar                        
00000811  SYSCFG_DL_GPIO_init                  
000009a5  __aeabi_dsub                         
000009a5  __subdf3                             
000009af  __adddf3                             
000009af  __aeabi_dadd                         
00000b37  __aeabi_idiv0                        
00000b39  No_MCU_Ganv_Sensor_Init              
00000cc1  GROUP1_IRQHandler                    
00000de1  main                                 
00000ef9  Set_PWM                              
00001005  __aeabi_ddiv                         
00001005  __divdf3                             
00001111  DL_Timer_initFourCCPWMMode           
00001215  DL_Timer_initTimerMode               
000012fd  TIMG0_IRQHandler                     
000013e1  __aeabi_dmul                         
000013e1  __muldf3                             
000014c5  OLED_ShowNum                         
000015a7  OLED_Init                            
00001685  DL_SYSCTL_configSYSPLL               
00001761  Get_Analog_value                     
00001831  normalizeAnalogValues                
000018db  OLED_ShowSignedNum                   
00001975  OLED_ShowString                      
00001a11  OLED_DrawPoint                       
00001aa1  SYSCFG_DL_PWM_0_init                 
00001b2d  SYSCFG_DL_initPower                  
00001bb9  OLED_Refresh                         
00001cc1  __TI_decompress_lzss                 
00001d3d  __gedf2                              
00001d3d  __gtdf2                              
00001db1  No_MCU_Ganv_Sensor_Init_Frist        
00001e25  OLED_WR_Byte                         
00001e91  convertAnalogToDigital               
00001efd  __cmpdf2                             
00001efd  __eqdf2                              
00001efd  __ledf2                              
00001efd  __ltdf2                              
00001efd  __nedf2                              
00001f65  __aeabi_dcmpeq                       
00001f79  __aeabi_dcmplt                       
00001f8d  __aeabi_dcmple                       
00001fa1  __aeabi_dcmpge                       
00001fb5  __aeabi_dcmpgt                       
00001fc9  OLED_Clear                           
00002029  DL_I2C_fillControllerTXFIFO          
00002089  SYSCFG_DL_I2C_OLED_init              
000020e1  Key                                  
00002135  Key_1                                
00002189  SYSCFG_DL_UART_0_init                
00002361  __aeabi_d2iz                         
00002361  __fixdfsi                            
000023ab  adc_getValue                         
000023f5  DL_UART_init                         
0000243d  OLED_DisplayTurn                     
00002485  SYSCFG_DL_ADC12_0_init               
000024cd  SYSCFG_DL_SYSCTL_init                
00002515  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002559  No_Mcu_Ganv_Sensor_Task_Without_tick 
0000259d  DL_ADC12_setClockConfig              
000025dd  UART0_IRQHandler                     
0000261d  __aeabi_uidiv                        
0000261d  __aeabi_uidivmod                     
000026d5  Get_Anolog_Value                     
00002711  __TI_auto_init_nobinit_nopinit       
0000274d  __muldsi3                            
000027bd  OLED_ColorTurn                       
000027f1  SYSCFG_DL_TIMER_0_init               
00002825  SYSCFG_DL_init                       
00002889  OLED_Pow                             
000028b9  SysTick_Handler                      
00002941  __aeabi_i2d                          
00002941  __floatsidf                          
00002a35  _c_int00_noargs                      
00002a83  DL_I2C_setClockConfig                
00002aa9  __aeabi_ui2d                         
00002aa9  __floatunsidf                        
00002b0d  delay_ms                             
00002c81  DL_Timer_setCaptCompUpdateMethod     
00002c9d  DL_Timer_setClockConfig              
00002ecd  DL_Timer_setCaptureCompareOutCtl     
00003079  Left_Control                         
0000308d  Right_Control                        
000030a1  Right_Little_Control                 
000030d9  DL_UART_setClockConfig               
000030eb  TI_memcpy_small                      
000030fd  __TI_decompress_none                 
00003131  DL_Timer_setCaptureCompareValue      
00003141  __TI_zero_init                       
00003151  Get_Digtal_For_User                  
0000315f  TI_memset_small                      
0000316d  SYSCFG_DL_SYSTICK_init               
00003179  __aeabi_memclr                       
00003179  __aeabi_memclr4                      
00003179  __aeabi_memclr8                      
00003185  get_systicks                         
00003191  scheduler_init                       
0000319d  DL_Common_delayCycles                
000031a9  __aeabi_memcpy                       
000031a9  __aeabi_memcpy4                      
000031a9  __aeabi_memcpy8                      
000031b1  abort                                
000031b7  ADC0_IRQHandler                      
000031b7  ADC1_IRQHandler                      
000031b7  AES_IRQHandler                       
000031b7  CANFD0_IRQHandler                    
000031b7  DAC0_IRQHandler                      
000031b7  DMA_IRQHandler                       
000031b7  Default_Handler                      
000031b7  GROUP0_IRQHandler                    
000031b7  HardFault_Handler                    
000031b7  I2C0_IRQHandler                      
000031b7  I2C1_IRQHandler                      
000031b7  NMI_Handler                          
000031b7  PendSV_Handler                       
000031b7  RTC_IRQHandler                       
000031b7  SPI0_IRQHandler                      
000031b7  SPI1_IRQHandler                      
000031b7  SVC_Handler                          
000031b7  TIMA0_IRQHandler                     
000031b7  TIMA1_IRQHandler                     
000031b7  TIMG12_IRQHandler                    
000031b7  TIMG6_IRQHandler                     
000031b7  TIMG7_IRQHandler                     
000031b7  TIMG8_IRQHandler                     
000031b7  UART1_IRQHandler                     
000031b7  UART2_IRQHandler                     
000031b7  UART3_IRQHandler                     
000031ba  C$$EXIT                              
000031bb  HOSTexit                             
000031bf  Reset_Handler                        
000031c3  _system_pre_init                     
000031c8  asc2_2412                            
00003f24  asc2_1608                            
00004514  asc2_1206                            
00004988  asc2_0806                            
00004c48  __TI_Handler_Table_Base              
00004c54  __TI_Handler_Table_Limit             
00004c5c  __TI_CINIT_Base                      
00004c6c  __TI_CINIT_Limit                     
00004c6c  __TI_CINIT_Warm                      
20200000  OLED_GRAM                            
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200480  gPWM_0Backup                         
2020053c  Flag_stop                            
20200540  Flag_stop1                           
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
2020054c  Run                                  
20200550  encoderA_cnt                         
20200554  encoderB_cnt                         
20200558  gpio_interrup1                       
2020055c  gpio_interrup2                       
20200560  task_num                             
20200568  rx_buff                              
20200668  uart_rx_buffer                       
202006e8  Anolog                               
202006f8  black                                
20200708  white                                
20200720  D_Num                                
20200724  delay_times                          
2020072a  uart_rx_index                        
2020072b  uart_rx_ticks                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[189 symbols]
